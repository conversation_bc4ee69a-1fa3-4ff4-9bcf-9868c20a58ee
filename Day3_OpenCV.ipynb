import numpy as np
import cv2
  
# load the image and convert it to grayscale
image = cv2.imread('./image/lenaColor.png')
# image = cv2.imread('./image/fruits.png')

orig = image.copy()
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# threshold default 10
fast = cv2.FastFeatureDetector_create(threshold=None, nonmaxSuppression=True)  # Fast feature detector
kps = fast.detect(gray, None)

print(f'keypoint counts\t\t: {len(kps)}\n'
      f'type\t\t\t: {type(kps)}\n'
      f'Threshold\t\t: {fast.getThreshold()}\n'
      f'nonmaxSuppression\t: {fast.getNonmaxSuppression()}\n'
      f'neighborhood\t\t: {fast.getType()}\n\n'
      f'kps[:10] :\n{kps[:10]}')
# Print all default params

image = cv2.drawKeypoints(gray, kps, None, flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)   # image : image output

cv2.imshow('Images', np.hstack([orig, image]))
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2
gray = cv2.imread('./image/lenaColor.png', 0)
# sift=cv2.xfeatures2d.SIFT_create()    # create object
sift = cv2.SIFT_create()
kp = sift.detect(gray, None)
print(f'kp[:5]\t:\n{kp[:5]}\n\n'
      f'len(kp)\t\t: {len(kp)}\n'
      f'kp[0].pt (x, y)\t: {kp[0].pt}\n'            # 座標會使用**二次插值（quadratic interpolation）**對位置進行微調
      f'kp[0].size\t: {kp[0].size}\n'               # 強度
      f'kp[0].response\t: {kp[0].response}\n'       # 回應強度
      f'kp[0].octave\t: {kp[0].octave}\n'           # 所在金字塔層級
      f'kp[0].class_id\t: {kp[0].class_id}' )       # 特徵點的類別 ID
      # f'kp[0].size\t: {kp[0].angel}')             # 方向角度

img = cv2.drawKeypoints(gray, kp, None, flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)   # output to 'img'
# img = cv2.drawKeypoints(gray, kp, None)   # out to 'img'
cv2.imshow('sift_kp', img)

cv2.imwrite('./image/lena_sift_kp.jpg', img)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2

# gray = cv2.imread('./image/mybaby.jpg', 0)
gray = cv2.imread('./image/lenaColor.png', 0)

# sift = cv2.xfeatures2d.SIFT_create()
sift = cv2.SIFT_create()
# kp = sift.detect(gray,None)

# directly find keypoints and descriptors in a single step
kp, des = sift.detectAndCompute(gray, None)   # kp指關鍵點, des指關鍵點的特徵描述
print(f'kps count : {len(kp)}\n'
      f'des[0] :\n{des[0:2]}\n'               # like HOG (Histogram of Oriented Gradients)
      f'len : {len(des[0])}')
img = cv2.drawKeypoints(gray, kp, None)

np.save('./image/mybaby_sift_kp', des)
cv2.imshow('SIFT', img)
# cv2.waitKey(0)

cv2.imwrite('./image/mybaby_sift.jpg', img)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import numpy as np
# image = cv2.imread('./image/blox.jpg')
image = cv2.imread('./image/lenaColor.png')

# image = cv2.imread('./image/lenaColor.png', cv2.COLOR_BGR2GRAY)

# sift_feature = cv2.xfeatures2d.SIFT_create()
sift_feature = cv2.SIFT_create()
sift_kp = sift_feature.detect(image)
sift_out = cv2.drawKeypoints(image, sift_kp, None)

orb_feature = cv2.ORB_create()
orb_kp  = orb_feature.detect(image)
orb_out  = cv2.drawKeypoints(image, orb_kp, None)

font = 2;    lt = 16
loc = (10, 40); color = (255, 255, 255)
cv2.putText(image, 'original', loc, font, 1, color, 2, lt)
cv2.putText(sift_out, 'sift', loc, font, 1, color, 2, lt)
cv2.putText(orb_out, 'orb', loc, font, 1, color, 2, lt)

image = cv2.hconcat([image, sift_out, orb_out])

cv2.imshow('image', image)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2

img = cv2.imread('./image/lenaColor.png', 1)
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

dst = cv2.cornerHarris(gray, blockSize=5, ksize=3, k=0.04)  
print(len(dst))
# blockSize:檢測的臨點數, ksize:sobel邊緣檢測的核, k:目標函式的一個引數（一般取值較小）

#result is dilated for marking the corners, not important
# dst = cv2.dilate(dst, None)

# Threshold for an optimal value, it may vary depending on the image.
img[dst>0.01*dst.max()]=[0,0,255]
cv2.imshow('dst', img)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
# ======== example 0 =================
# img1 = cv2.resize(cv2.imread('./image/box_in_scene.png'), None, fx=1.4, fy=1.4)
# img2 = cv2.imread('./image/box.png')

# ======== example 1 =================
# img1 = cv2.imread('./image/testpic.jpg')
# img2 = cv2.imread('./image/testpic2.jpg')

# ======== example 2 =================
# img1 = cv2.imread('./image/aiotbooks.jpg')
# img2 = cv2.imread('./image/aiotimage.jpg')

# ======== example 3 =================
img1 = cv2.imread('./image/mario.jpg')
img2 = cv2.imread('./image/marioCoin.jpg')

feature = cv2.SIFT_create()
# feature = cv2.xfeatures2d.SURF_create()   # This algorithm is patented and is excluded in this configuration

kp1, des1 = feature.detectAndCompute(img1, None)
kp2, des2 = feature.detectAndCompute(img2, None)
print(len(kp1), len(kp2))

# ========= BFMatcher ================
bf = cv2.BFMatcher()
matches = bf.knnMatch(des1, des2, k=2)  # k Count of best matches found per each query descriptor
# matches = bf.Match(des1, des2)  # k Count of best matches found per each query descriptor
# matches = sorted(matches, key = lambda x:x.distance)

print(f'len(matches)\t\t= {len(matches)} 組, \tk = 2\n')

for i in range(3) :
    print(f'matches[{i}]\t\t= {matches[i]}')
    for j in range(2) :
        print(f'matches[{i}][{j}].queryIdx\t= {matches[i][j].queryIdx}\n'
              f'matches[{i}][{j}].trainIdx\t= {matches[i][j].trainIdx}\n'
              f'matches[{i}][{j}].distance\t= {matches[i][j].distance:.2f}\n')
    print('-'*80)

good = []
for m, n in matches:
    if n.distance >  m.distance*2.4:
    # if (m.distance < .35 * n.distance):      # 如果第一個鄰近距離比第二個鄰近距離的0.4倍小，則保留 try 0.5, 0.6, 0.7
    # if m.distance / n.distance < 0.4:     # 如果第一個鄰近距離比第二個鄰近距離的0.4倍小，則保留 try 0.5, 0.6, 0.7
    # if m.distance < 100:                  # 如果第一個鄰近距離
        good.append(m)
print(f'Matching points : {len(good)}')
img3 = cv2.drawMatchesKnn(img1, kp1, img2, kp2, [good], outImg=None, 
                          flags=cv2.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS)

img3 = cv2.resize(img3, None, fx=.8, fy=.8)
cv2.imshow('video', img3)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2

# ======== example 0 =================
# img1 = cv2.imread('./image/box_in_scene.png')
# img2 = cv2.imread('./image/box.png')

# ======== example 1 =================
# img1 = cv2.imread('./image/testpic.jpg')
# img2 = cv2.imread('./image/testpic2.jpg')

# ======== example 2 =================
img1 = cv2.imread('./image/aiotbooks01.jpg')
img2 = cv2.imread('./image/aiotimage.jpg')

orb = cv2.ORB_create()
kp1, des1 = orb.detectAndCompute(img1, None)
kp2, des2 = orb.detectAndCompute(img2, None)
print(len(kp1), len(kp2))

bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
matches = bf.match(des1, des2)

print(len(matches))

matches = sorted(matches, key=lambda x:x.distance)
img3 = cv2.drawMatches(img1, kp1, img2, kp2, matches[:30], outImg=None,
                       flags = cv2.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS)

width, height, channel = img3.shape
ratio = width / height
img3 = cv2.resize(img3, (1024, int(1024 * ratio)))
cv2.imshow('image', img3)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2
from matplotlib import pyplot as plt

img1 = cv2.imread('./image/box_in_scene.png')  # queryImage
img2 = cv2.imread('./image/box.png')  # trainImage

# Initiate SIFT detector
# sift = cv2.xfeatures2d.SIFT_create()
sift = cv2.SIFT_create()

# find the keypoints and descriptors with SIFT
kp1, des1 = sift.detectAndCompute(img1, None)
kp2, des2 = sift.detectAndCompute(img2, None)
print(len(kp1), len(kp2))
# ============= FLANN parameters ===========
FLANN_INDEX_KDTREE = 0
index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
search_params = dict(checks=50)  # or pass empty dictionary

# ============= Brute-Force ============
# bf = cv2.BFMatcher()
# matches = bf.knnMatch(des1, des2, k=2)

# ============= Flann ============
flann = cv2.FlannBasedMatcher(index_params, search_params)
matches = flann.knnMatch(des1, des2, k=2)

# Need to draw only good matches, so create a mask
matchesMask = [[0, 0] for i in range(len(matches))]

# ratio test as per Lowe's paper
for i, (m, n) in enumerate(matches):
    # if m.distance < 0.6 * n.distance:
    if n.distance >  m.distance*1.5:
        matchesMask[i] = [1, 0]

draw_params = dict(matchColor=(0, 255, 0),
                   singlePointColor=(255, 0, 0),
                   matchesMask=matchesMask,
                   flags=0)

img3 = cv2.drawMatchesKnn(img1, kp1, img2, kp2, matches, None, **draw_params)

plt.figure(figsize=(12, 6))
plt.imshow(img3, ), plt.show()

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2
from matplotlib import pyplot as plt

MIN_MATCH_COUNT = 10
# =======================================
# img1 = cv2.imread('./image/box_in_scene.png', 0)    # Source Image
# img2 = cv2.imread('./image/box.png', 0)             # Check Image

#=======================================
# img1 = cv2.imread('./image/jp_02.png', 0)    # Source Image
# img2 = cv2.imread('./image/jp_01.png', 0)    # Check Image

#=======================================
img1 = cv2.imread('./image/douglas_02.png', 0)    # Source Image
img2 = cv2.imread('./image/douglas_01.png', 0)    # Check Image

# Initiate SIFT detector
# sift = cv2.xfeatures2d.SIFT_create()
sift = cv2.SIFT_create()

# find the keypoints and descriptors with SIFT
kp1, des1 = sift.detectAndCompute(img1, None)
kp2, des2 = sift.detectAndCompute(img2, None)

FLANN_INDEX_KDTREE = 0
index_params = dict(algorithm = FLANN_INDEX_KDTREE, trees = 5)
search_params = dict(checks = 50)

flann = cv2.FlannBasedMatcher(index_params, search_params)

matches = flann.knnMatch(des1, des2, k=2)

# store all the good matches as per Lowe's ratio test.
good = []
for m,n in matches:
    if m.distance < 0.7*n.distance:
        good.append(m)

if len(good)>MIN_MATCH_COUNT:
    src_pts = np.float32([kp1[m.queryIdx].pt for m in good]).reshape(-1,1,2)
    dst_pts = np.float32([kp2[m.trainIdx].pt for m in good]).reshape(-1,1,2)
   
    M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC,5.0)
    matchesMask = mask.flatten().tolist()

    h,w = img1.shape
    pts = np.float32([[0,0],[0,h-1],[w-1,h-1],[w-1,0]]).reshape(-1,1,2)
    dst = cv2.perspectiveTransform(pts,M)

    img2 = cv2.polylines(img2,[np.int32(dst)],True,255,3, cv2.LINE_AA)
else:
    print(f'Not enough matches are found - {len(good)}/{MIN_MATCH_COUNT}')
    matchesMask = None

draw_params = dict(matchColor = (0,255,0), # draw matches in green color
                   singlePointColor = None,
                   matchesMask = matchesMask, # draw only inliers
                   flags = 2)

img3 = cv2.drawMatches(img1, kp1, img2, kp2, good, None,**draw_params)

# cv2.imshow('Result', img3)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

plt.figure(figsize=(10, 8))
plt.imshow(img3, 'gray'),plt.show()

import numpy as np
import cv2
import matplotlib.pyplot as plt

o = cv2.imread('./image/lenaColor.png', 0)
cv2.imshow('original', o)
cv_hist256 = cv2.calcHist([o], [0], None, [256], [0,255])
cv_hist16 = cv2.calcHist([o], [0], None, [16], [0,255])

hist, bins = np.histogram(o.flatten(), 256, [0,256])  # 拉平
cdf = hist.cumsum()                                  # 累加 cdf : cumulate distribution function
cdf_normalized = cdf / cdf.max() * hist.max()         # 標準化
print(f'hist max()\t: {hist.max()},\ncdf.max()\t: {cdf.max()}')

fig=plt.figure(figsize=(12, 6))
plt.subplot(221)  
plt.hist(o.flatten(), 256); plt.title('bin=256')       # flatten 將多維陣列轉換為一維陣列的功能 like flatten
plt.plot(cdf_normalized, color='r')                 # plot cdf

plt.subplot(222)  
plt.hist(o.flatten(), 16);  plt.title('bin=16')

plt.subplot(223)  
plt.plot(cv_hist256, color='y');  plt.title('cv_hist256')

plt.subplot(224)  
plt.plot(cv_hist16, color='r');  plt.title('cv_hist16')

plt.show()

cv2.waitKey()
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import matplotlib.pyplot as plt

o=cv2.imread('./image/lenaColor.png')
cv2.imshow('original', o)

cv_histb = cv2.calcHist([o], [0], None, [256], [0,255])   # [0] B
cv_histg = cv2.calcHist([o], [1], None, [256], [0,255])   # [1] G
cv_histr = cv2.calcHist([o], [2], None, [256], [0,255])   # [2] R

mean, std = cv2.meanStdDev(o);    print(f'mean :\n{mean}\n\nstd :\n{std}')
fig=plt.figure(figsize=(14, 8))

plt.subplot(221)
plt.plot(cv_histb, color='b');  plt.title('cv_hist b');  plt.axvline(x=mean[0], color='r')
plt.text(mean[0]+2, cv_histb.max()*.95, f'avg.:{mean[0][0]:.2f}', color='r', fontsize=10)        

plt.subplot(222)
plt.plot(cv_histg, color='g');  plt.title('cv_hist g');  plt.axvline(x=mean[1], color='r')
plt.text(mean[1]+2, cv_histg.max()*.95, f'avg.:{mean[1][0]:.2f}', color='r', fontsize=10)        

plt.subplot(223)  
plt.plot(cv_histr, color='r');  plt.title('cv_hist r');  plt.axvline(x=mean[2], color='r')
plt.text(mean[2]+2, cv_histr.max()*.95, f'avg.:{mean[2][0]:.2f}', color='r', fontsize=10)        

color = ('b', 'g', 'r')
plt.subplot(224);  plt.title('cv_hist rgb')
for i, col in enumerate(color):
    histr = cv2.calcHist([o], [i], None, [256], [0, 256])
    plt.plot(histr, color = col)

plt.show()

cv2.waitKey()
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import numpy as np
import matplotlib.pyplot as plt

o = cv2.imread('./image/lenaColor.png', 0)
equ=cv2.equalizeHist(o)

# cv2.imshow('original', o)
# cv2.imshow('equ', equ)
print(o.size)

hist, bins = np.histogram(o.flatten(),256,[0,256])
cdf = hist.cumsum()
cdf_normalized_o = cdf * hist.max()/ cdf.max()

hist, bins = np.histogram(equ.flatten(),256,[0,256])
cdf = hist.cumsum()
cdf_normalized_eq = cdf * hist.max()/ cdf.max()

fig=plt.figure(figsize=(14, 4))

plt.subplot(121)
plt.hist(o.flatten(), 256)
plt.plot(cdf_normalized_o, color='r')

plt.subplot(122)  
plt.hist(equ.flatten(), 256)
plt.plot(cdf_normalized_eq, color='r')
plt.show()

cv2.imshow('original', o)
cv2.imshow('gray_equ', equ)
cv2.waitKey()
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import numpy as np

img = cv2.imread('./image/lenaColor.png')
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

equ = cv2.equalizeHist(gray)     # 灰度圖均衡化

result1 = np.hstack((gray, equ))  # 水平拼接原圖和均衡圖
cv2.imshow('grey_equ', result1)

(b, g, r) = cv2.split(img)        # 彩色影像均衡化,需要分解通道 對每一個通道均衡化
bH = cv2.equalizeHist(b)
gH = cv2.equalizeHist(g)
rH = cv2.equalizeHist(r)

equ2 = cv2.merge((bH, gH, rH))    # 合併每一個通道

result2 = np.hstack((img, equ2)) # 水平拼接原圖和均衡圖
cv2.imshow('bgr_equ', result2)

cv2.waitKey()
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import numpy as np

img = cv2.imread('./image/lenaColor.png', 0)

equ = cv2.equalizeHist(img) #            全域性直方圖均衡

# 自適應直方圖均衡
clahe = cv2.createCLAHE(clipLimit = 2.0, tileGridSize = (8, 8))  # default cliplimit  = 40 
cl1 = clahe.apply(img)

result1 = np.hstack((img, equ, cl1))   # 水平拼接三張影像
cv2.imshow('clahe_result', cv2.resize(result1, (0,0), fx=.8, fy=.8))

fig=plt.figure(figsize=(12, 8))

plt.subplot(221)
plt.hist(img.flatten(), 256, [0, 255], label='original image'), plt.legend()

plt.subplot(222)
plt.hist(equ.flatten(), 256, [0, 255], label='equalize image', color='orange'), plt.legend()

plt.subplot(223)
plt.hist(cl1.flatten(), 256, [0, 255], label='clahe image', color='g'), plt.legend()

plt.subplot(224)
plt.hist(img.flatten(), 256, [0, 255], label='original image')
plt.hist(equ.flatten(), 256, [0, 255], label='equalize image')
plt.hist(cl1.flatten(), 256, [0, 255], label='clahe image')
plt.legend()

cv2.waitKey()
cv2.destroyAllWindows()

import numpy as np
import cv2

# cap = cv2.VideoCapture('./video/chaplin.mp4')   # play video file
cap = cv2.VideoCapture(0)                         # from camera

fps = cap.get(cv2.CAP_PROP_FPS)               # Frame Per Second
F_Count = cap.get(cv2.CAP_PROP_FRAME_COUNT)   # frame count
print(f'fps : {fps:.2f} f/s, Frame_Count : {F_Count}')

while cap.isOpened():
    ret, frame = cap.read()
    if not ret or cv2.waitKey(1) == 27: break

    frame = cv2.flip(frame, 1)   # left side right
    # frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    cv2.imshow('frame',frame)
    c=cv2.waitKey(30)            # 25 ms per frame     1/fps

cap.release()
cv2.destroyAllWindows()
cv2.waitKey(1)

from IPython.display import HTML

HTML('''<video alt='test' controls>
        <source src='./video/chaplin.mp4' type='video/mp4'></video>''')

import cv2
# import datetime as dt
from datetime import datetime as dt
now_dt = dt.now().strftime('%m/%d/%Y %H:%M:%S')

# cv2.namedWindow('frame', cv2.WINDOW_NORMAL)
# cap = cv2.VideoCapture('./video/chaplin.mp4')   # play video file
cap = cv2.VideoCapture(0)                         # from camera

font = 2;    lt = 16
print(f'frame_w\t\t: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}\n'
      f'frame_fps\t: {cap.get(cv2.CAP_PROP_FPS)}\n'
      f'frame_count\t: {cap.get(cv2.CAP_PROP_FRAME_COUNT)}')

# ratio = cap.get(cv2.CAP_PROP_FRAME_WIDTH) / cap.get(cv2.CAP_PROP_FRAME_HEIGHT)  # cap.get得到相機/視訊檔案的屬性
# w = 500;  h = int(w / ratio)

# cv2.resizeWindow('frame', w, h)       # change frame size
count=0
while True:
    ret, frame = cap.read()                          #  read frame : ret: True/ False,  frame:image
    if not ret or cv2.waitKey(30) == 27: break             # wait for 1 ms     
    
    frame = cv2.flip(frame, 1)                       # 0 : 上下左右顛倒,  -1 : 上下顛倒
    
    if cv2.waitKey(1)==ord('t') or cv2.waitKey(1)==ord('T'):
        cv2.putText(frame, now_dt, (100, 300), font, 1, (0,0,255), 2, lt)
        cv2.imwrite(f'./image/frame{count}.jpg', frame)     # save frame as JPEG file          
        print(f'save image : frame{count}.jpg')
        count+=1 
        
#     frame = cv2.resize(frame, (w, h))
    cv2.putText(frame, f'{cap.get(cv2.CAP_PROP_POS_FRAMES):.0f} frames, {cap.get(cv2.CAP_PROP_POS_MSEC):.0f} ms', 
                (50, 250), font, .8, (0,0,255), 2, lt)

    cv2.imshow('frame', frame)
cap.release()
cv2.destroyAllWindows()
cv2.waitKey(1)   

import cv2

cap = cv2.VideoCapture('./video/vtest.avi')
# cap = cv2.VideoCapture('./video/overpass.mp4')
# cap = cv2.VideoCapture('./video/car_chase_01.mp4')

# tracker = cv2.TrackerCSRT_create()

# tracker = cv2.TrackerBoosting_create()   # error
tracker = cv2.TrackerMIL_create()
# tracker = cv2.TrackerKCF_create()
# tracker = cv2.TrackerTLD_create()            # error
# tracker = cv2.TrackerMedianFlow_create()       # error
# tracker = cv2.TrackerGOTURN_create()          # error
# tracker = cv2.TrackerMOSSE_create()         # error

roi = None
while True:
    ret, frame = cap.read()
    if not ret or cv2.waitKey(40) == 27  : break
    
    if roi is None:
        roi = cv2.selectROI('frame', frame)
        if roi != (0, 0, 0, 0):
            tracker.init(frame, roi)

    success, rect = tracker.update(frame)
    if success: 
        (x, y, w, h) = [int(i) for i in rect]
        cv2.rectangle(frame, (x,y), (x+w, y+h), (0, 255, 0), 2)

    cv2.imshow('frame', frame)
cap.release()
cv2.destroyAllWindows()
cv2.waitKey(1) 

import cv2, time

cap = cv2.VideoCapture(0)
# tracker = cv2.TrackerCSRT_create()

# tracker = cv2.TrackerBoosting_create()   # error
tracker = cv2.TrackerMIL_create()
# tracker = cv2.TrackerKCF_create()
# tracker = cv2.TrackerTLD_create()            # error
# tracker = cv2.TrackerMedianFlow_create()       # error
# tracker = cv2.TrackerGOTURN_create()          # error
# tracker = cv2.TrackerMOSSE_create()         # error
time.sleep(3)
roi = None
while True:
    ret, frame = cap.read()
    if not ret or cv2.waitKey(1) == 27: break
    
    frame = cv2.flip(frame, 1)
    
    if roi is None:
        roi = cv2.selectROI('frame', frame)
        if roi != (0, 0, 0, 0):
            tracker.init(frame, roi)

    success, rect = tracker.update(frame)
    if success: 
        (x, y, w, h) = [int(i) for i in rect]
        cv2.rectangle(frame, (x,y), (x+w, y+h), (0, 255, 0), 2)

    cv2.imshow('frame', frame)
cap.release()        
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2

# 指定 ROI 座標位置
RECT = ((320, 20), (570, 390))
(left, top), (right, bottom) = RECT

def roiarea(frame):                  # 取出 ROI 子畫面
    return frame[top:bottom, left:right]

def replaceroi(frame, roi):             # 將 ROI 區域貼回到原畫面
    frame[top:bottom, left:right] = roi
    return frame

cap = cv2.VideoCapture(0)               # 開啟攝影機, 讀取畫面
ratio = cap.get(cv2.CAP_PROP_FRAME_WIDTH) / cap.get(cv2.CAP_PROP_FRAME_HEIGHT)

w = 800
h = int(w / ratio)

while True:
    ret, frame = cap.read()
    if not ret or cv2.waitKey(30) == 27: break
    frame = cv2.resize(frame, (w, h))
    frame = cv2.flip(frame, 1)

    roi = roiarea(frame)                   # 取出子畫面
    roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)        # BGR to HSV
#     roi = cv2.cvtColor(roi, cv2.COLOR_BGR2XYZ)          # BGR to XYZ
    
    frame = replaceroi(frame, roi)         # 將處理完的子畫面貼回到原本畫面中
    
    cv2.rectangle(frame, RECT[0], RECT[1], (0,0,255), 2)      # 在 ROI 範圍處畫個框
    cv2.imshow('frame', frame)
    
cap.release()
cv2.destroyAllWindows()
cv2.waitKey(1) 

import dlib
import cv2
print(f'dlib ver.\t: {dlib.__version__}\n\t\t: {dlib.__file__}')

# img = cv2.imread('./image/dlib.jpg')   # 讀取照片圖檔
# img = cv2.imread('./image/faces02.png')
img = cv2.imread('./image/face03.jpg')

detector = dlib.get_frontal_face_detector()  # Dlib 的人臉偵測器

face_rects = detector(img, 0)   # try (img, 1)   # 偵測人臉
print(f'detected face numder : {len(face_rects)}')

# 取出所有偵測的結果
for i, d in enumerate(face_rects):
    x1, y1, x2, y2 = d.left(), d.top(), d.right(), d.bottom()
    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 4, cv2.LINE_AA)  # 以方框標示偵測的人臉
    cv2.putText(img, f'{i}', (x1, y1-10), 0, .7, (0, 255, 0), 2, 16)
cv2.imshow('Face Detection', img)     # 顯示結果

cv2.waitKey()
cv2.destroyAllWindows()
cv2.waitKey(1)

!pip list

import dlib
import cv2

img = cv2.imread('./image/dlib.jpg')
# img = cv2.imread('./image/faces02.png')

detector = dlib.get_frontal_face_detector()
font = 2;    lt = 16
face_rects, scores, idx = detector.run(img, 1, -0.8)   # 偵測人臉，輸出分數
print(f'detected face numder : {len(face_rects)}')

for i, d in enumerate(face_rects):
    x1, y1, x2, y2 = d.left(), d.top(), d.right(), d.bottom()

    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 3, cv2.LINE_AA)
    cv2.putText(img, f'{scores[i]:.2f}({idx[i]:0.0f})', (x1, y1), font,       # 標示分數
                0.7, (0, 0, 255), 1, lt)

cv2.imshow('Face Detection', img)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import dlib
import cv2

cap = cv2.VideoCapture('./video/Alec_Baldwin.mp4')    # 開啟影片檔案

w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))        # 取得畫面尺寸
h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = cap.get(cv2.CAP_PROP_FPS)
fourcc = cv2.VideoWriter_fourcc(*'XVID')             # 使用 XVID 編碼 mpeg-4
cap.set(cv2.CAP_PROP_POS_MSEC, 10000)               #  從第 10 秒開始撥放

# 建立 VideoWriter 物件，輸出影片至 output.avi，FPS 值為 20.0
out = cv2.VideoWriter('./video/Alec_Baldwin_out.mp4', fourcc, fps, (w, h))
font = 2;    lt = 16
detector = dlib.get_frontal_face_detector()          # Dlib 的人臉偵測器

while(cap.isOpened() and cap.get(cv2.CAP_PROP_POS_MSEC)) < 20000:    # 以迴圈從影片檔案讀取影格，並顯示出來
    ret, frame = cap.read()
    if not ret or cv2.waitKey(1) == 27 : break
    
    face_rects, scores, idx = detector.run(frame, 0, -.25)  # 偵測人臉
    for i, d in enumerate(face_rects):               # 取出所有偵測的結果
        x1, y1, x2, y2 = d.left(), d.top(), d.right(), d.bottom()

        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 4, cv2.LINE_AA) # 以方框標示偵測的人臉
        cv2.putText(frame, f'{scores[i]:.2f}, ({idx[i]:0.0f})', (x1, y1), font,          # 標示分數
                    0.7, (255, 255, 255), 1, lt)
            
    cv2.imshow('Face Detection', frame)           # 顯示結果
    out.write(frame)                               # 寫入影格

cap.release()
out.release()
cv2.destroyAllWindows()
cv2.waitKey(1)

import dlib
import cv2

cap = cv2.VideoCapture(0)   # 開啟影片檔案
detector = dlib.get_frontal_face_detector() # Dlib 的人臉偵測器
font = 2;    lt = 16

while(cap.isOpened()):               # 以迴圈從影片檔案讀取影格，並顯示出來
    ret, frame = cap.read()
    if not ret or cv2.waitKey(1) == 27 : break
    
    face_rects, scores, idx = detector.run(frame, 0)  # 偵測人臉

    for i, d in enumerate(face_rects):                  # 取出所有偵測的結果
        x1, y1, x2, y2 = d.left(), d.top(), d.right(), d.bottom()

        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 4, cv2.LINE_AA) # 以方框標示偵測的人臉
        cv2.putText(frame, f'{scores[i]:.2f}, ({idx[i]:0.0f})', (x1, y1), font,    # 標示分數
                    0.7, (255, 255, 255), 1, lt)

    cv2.imshow('Face Detection', frame)                      # 顯示結果

cap.release()
cv2.destroyAllWindows()
cv2.waitKey(1)

import numpy as np
import cv2
import dlib

detector = dlib.get_frontal_face_detector()
# predictor = dlib.shape_predictor(r'./model/shape_predictor_68_face_landmarks.dat')
predictor = dlib.shape_predictor(r'./model/shape_predictor_5_face_landmarks.dat')
font = 2;    lt = 16            # 利用cv2.putText輸出1-68

img = cv2.imread('./image/face68.JFIF')     # cv2讀取影像
# img = cv2.imread('./image/LenaColor4.jpg')     # cv2讀取影像
img_gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)  # 取灰度

rects = detector(img_gray, 0)             # 人臉數rects
for i in range(len(rects)):
    landmarks = [[p.x, p.y] for p in predictor(img, rects[i]).parts()]
    for idx, pos in enumerate(landmarks):
        cv2.circle(img, pos, 3, color=(0, 255, 0)) # 利用cv2.circle給每個特徵點畫一個圈，共68個
        cv2.putText(img, str(idx+1), pos, font, 0.5, (0, 0, 255), 1, lt)

cv2.imshow('img', img)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

# 匯入必要的library
import cv2
from scipy.io import loadmat
# from skimage import io
import dlib, os, glob, re
# from imutils import paths

options = dlib.simple_object_detector_training_options()
images = [] # 存放相片圖檔
boxes = []  # 存放Annotations

categories = glob.glob(r'./dlib_ObjectCategories10/**/*.jpg', recursive=True)
annotation = glob.glob(r'./dlib_Annotations10/**/ann*.mat', recursive=True)

for cate, anno in zip(categories, annotation) :
    print(cate, anno)
    xywh = loadmat(anno)['box_coord']
    # 取出annotations資訊繪成矩形物件，放入boxes變數中。
    # (x, y, w, h) = (b.left(), b.top(), b.right(), b.bottom())
    bb = [dlib.rectangle(left=int(x), top=int(y), right=int(w), bottom=int(h)) for (y, h, x, w) in xywh]
    boxes.append(bb)
    # images.append(io.imread(cate))          #將圖片放入images變數
    images.append(cv2.imread(cate))          #將圖片放入images變數
print('done', len(images), len(boxes))

#丟入三個參數開始訓練
options = dlib.simple_object_detector_training_options()
print('[INFO] training detector...')
detector = dlib.train_simple_object_detector(images, boxes, options)
 
# 將訓練結果匯出到檔案
print('[INFO] dumping classifier to file...')
detector.save('./dlib_output/model.svm')           # save

detector = dlib.simple_object_detector('./dlib_output/model.svm')   # load back

# 圖形化顯示 Histogram of Oriented Gradients（簡稱HOG）
win = dlib.image_window()
win.set_image(detector)
# dlib.hit_enter_to_continue()

import dlib
import cv2
from imutils import paths
#載入訓練好的detector
# detector = dlib.simple_object_detector(detector)
 
#載入測試圖片逐張進行
for idx, testingPath in enumerate(paths.list_images('./dlib_ObjectCategories10/accordion')):
# for idx, testingPath in enumerate(paths.list_images('./dlib_ObjectCategories10/camera')):
#讀取圖片並執行dector並產生矩形物件以便用於標記辨識出的部份
    image = cv2.imread(testingPath)
    boxes = detector(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

    for b in boxes:                    #在圖片上繪出該矩形
        (x, y, w, h) = (b.left(), b.top(), b.right(), b.bottom())
        cv2.rectangle(image, (x, y), (w, h), (0, 255, 0), 2)

    cv2.imshow(str(idx), image)         #顯示圖片
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2

# pictPath = r'./model/haarcascade_eye.xml'                   # eye
# pictPath = r'./model/haarcascade_frontalface_alt.xml'     # frontal face
pictPath = r'./model/haarcascade_smile.xml'                 # smile

body_cascade = cv2.CascadeClassifier(pictPath)      # 建立辨識物件
# img = cv2.imread("./image/dlib.jpg")                     # 讀取影像
# img = cv2.imread("./image/fullbody.jpg")                     # 讀取影像
img = cv2.imread("./image/faces01.jpg");   img=cv2.resize(img, None, fx=.8, fy=.8)

bodies = body_cascade.detectMultiScale(img, 
                                       scaleFactor=1.1,
                                       minNeighbors = 2, 
                                       minSize=(5, 5))
# 標註身體
for (x,y,w,h) in bodies:
    cv2.rectangle(img,(x,y),(x+w,y+h),(0,255,0),2)  # 藍色框住身體
cv2.imshow("Body", img)                             # 顯示影像

cv2.waitKey(0)
cv2.destroyAllWindows()

import cv2

pictPath = r'./model/haarcascade_fullbody.xml'
body_cascade = cv2.CascadeClassifier(pictPath)      # 建立辨識物件

img = cv2.imread("./image/fullbody.jpg")                     # 讀取影像
img=cv2.resize(img, None, fx=1.4, fy=1.4)
# 
bodies = body_cascade.detectMultiScale(img, 
                                       scaleFactor=1.1, 
                                       minNeighbors = 3, 
                                       minSize=(50,50))
# 標註身體
for (x,y,w,h) in bodies:
    cv2.rectangle(img,(x,y),(x+w,y+h),(0,255,0),2)  # 藍色框住身體
cv2.imshow("Body", img)                             # 顯示影像

cv2.waitKey(0)
cv2.destroyAllWindows()

# ch27_9.py
import cv2

pictPath = r'./model/haarcascade_upperbody.xml'
# pictPath = r'./model/haarcascade_lowerbody.xml'

body_cascade = cv2.CascadeClassifier(pictPath)      # 建立辨識物件
img = cv2.imread("./image/fullbody.jpg")                     # 讀取影像
img=cv2.resize(img, None, fx=1.4, fy=1.4)

bodies = body_cascade.detectMultiScale(img, 
                                       scaleFactor=1.1, 
                                       minNeighbors = 3, 
                                       minSize=(20,20))
# 標註身體
for (x,y,w,h) in bodies:
    cv2.rectangle(img,(x,y),(x+w,y+h),(0,255,0),2)  # 藍色框住身體
cv2.imshow("Body", img)                             # 顯示影像

cv2.waitKey(0)
cv2.destroyAllWindows()

# ch27_18.py
import cv2

pictPath = r'./model/haarcascade_russian_plate_number.xml'
car_cascade = cv2.CascadeClassifier(pictPath)       # 建立辨識物件
# img = cv2.imread("./image/CarPlate.jpg")
img = cv2.imread("./image/CarPlate1.jpg")
img=cv2.resize(img, None, fx=1.2, fy=1.2)

# 讀取影像
plates = car_cascade.detectMultiScale(img, 
                                      scaleFactor=1.025, 
                                      minNeighbors = 2, 
                                      minSize=(20,20))
# 將車牌框起來, 由於有可能找到好幾個臉所以用迴圈繪出來
for (x,y,w,h) in plates:
    cv2.rectangle(img,(x,y),(x+w,y+h),(0,255,0),2)  # 藍色框住車牌
cv2.imshow("Car Plate", img)                        # 顯示影像

cv2.waitKey(0)
cv2.destroyAllWindows()

import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:/Program Files/Tesseract-OCR/tesseract.exe'

# IOS
# pytesseract.pytesseract.tesseract_cmd = r'/opt/homebrew/bin/tesseract'
pytesseract.get_tesseract_version()

import pytesseract
import cv2

# img = cv2.imread('./image/tess01.jpeg', 0)
# img = cv2.resize(img, (650, 850))

# img = cv2.imread('./image/starbucks.jpg', 0)
# img=cv2.resize(img, (850, 650))

# img = cv2.imread('./image/CarPlate1.jpg', 0)   # CarPlate1, 2
# img = cv2.imread('./image/CarPlate2.jpg', 0)   # CarPlate1, 2
img = cv2.imread('./image/CarPlate3.jpg', 0)   # CarPlate1, 2
# img=cv2.resize(img, (450, 550))

cv2.imshow('original', img)

pytesseract.pytesseract.tesseract_cmd = r'C:/Program Files/Tesseract-OCR/tesseract.exe'

# IOS
# pytesseract.pytesseract.tesseract_cmd = r'/opt/homebrew/bin/tesseract'

custom_config = ''
# custom_config = r'--oem 3 --psm 11'

text = pytesseract.image_to_string(img, lang='eng', config=custom_config)  # chi_tra, chi_sim 修改 lang 參數變就可以
print(text)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import pytesseract
import cv2

# img = cv2.imread('./image/OCR7.jpg', 0)
img = cv2.imread('./image/tess02.jpg', 0)
# img = cv2.imread('./image/traffic_plate01.jpg', 0)

img1=cv2.resize(img, (650, 850))
cv2.imshow('original', img)

pytesseract.pytesseract.tesseract_cmd = r'C:/Program Files/Tesseract-OCR/tesseract.exe'
# IOS
# pytesseract.pytesseract.tesseract_cmd = r'/opt/homebrew/bin/tesseract'

text = pytesseract.image_to_string(img, lang='chi_tra')  # try : chi_tra+eng, chi_sim
print(text)

cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

text.replace(' ', '').replace('.', ', ').replace('\n', '').replace('‧', '')

import cv2 
import pytesseract

img = cv2.imread('./image/tess01.jpeg', 0)
img=cv2.resize(img, (650, 850))

# Adding custom options
custom_config = r'--oem 1 --psm 3'
text = pytesseract.image_to_string(img, config=custom_config)
print(text)

cv2.imshow('img', img)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import pytesseract

img = cv2.imread('./image/tess01.jpeg')
img=cv2.resize(img, (650, 850))

h, w, c = img.shape
boxes = pytesseract.image_to_boxes(img) 
for b in boxes.splitlines():
    b = b.split(' ')
    img = cv2.rectangle(img, (int(b[1]), h-int(b[2])), (int(b[3]), h - int(b[4])), (0, 255, 0), 2)

cv2.imshow('img', img)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)

import cv2
import pytesseract
from pytesseract import Output

img = cv2.imread('./image/tess01.jpeg')
img=cv2.resize(img, (650, 850))
d = pytesseract.image_to_data(img, output_type=Output.DICT)
print(f'd.type : {type(d)}\n\n'
      f'd.keys() :\n{d.keys()}\n\n'
      f'd :\n{d}')

n_boxes = len(d['text'])
for i in range(n_boxes):
    if float(d['conf'][i]) > 60:
        (x, y, w, h) = (d['left'][i], d['top'][i], d['width'][i], d['height'][i])
        img = cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 1)

cv2.imshow('img', img)
cv2.waitKey(0)
cv2.destroyAllWindows()
cv2.waitKey(1)